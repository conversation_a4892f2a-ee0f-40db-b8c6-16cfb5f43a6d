// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';

import '../controllers/live_stream_controller.dart';
import '../controllers/live_stream_chat_controller.dart';
import '../models/live_stream_model.dart';
import 'widgets/live_stream_chat.dart';
import 'widgets/live_stream_controls.dart';
import 'widgets/stream_filter_panel.dart';
import 'widgets/filtered_camera_preview.dart';
import 'share_live_stream_sheet.dart';
import '../../home/<USER>/views/home_view.dart';
import '../../home/<USER>/controllers/home_controller.dart';

class LiveStreamView extends StatefulWidget {
  final LiveStreamModel stream;
  final bool isStreamer;

  const LiveStreamView({
    super.key,
    required this.stream,
    required this.isStreamer,
  });

  @override
  State<LiveStreamView> createState() => _LiveStreamViewState();
}

class _LiveStreamViewState extends State<LiveStreamView> {
  late final LiveStreamController controller;
  late final LiveStreamChatController chatController;
  bool _showChat = true;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    controller = GetIt.I.get<LiveStreamController>();
    chatController = GetIt.I.get<LiveStreamChatController>();

    // Set up stream ended callback for participants
    if (!widget.isStreamer) {
      controller.onStreamEndedCallback = () {
        if (mounted) {
          _showStreamEndedDialog();
        }
      };

      // Set up callback for when user is removed/banned
      controller.onStreamEndedWithReasonCallback =
          (String? reason, bool isBanned) {
        if (mounted) {
          _handleRemovalFromStream(reason, isBanned);
        }
      };
    }

    // Initialize stream asynchronously
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller
          .initializeStream(
        stream: widget.stream,
        isStreamer: widget.isStreamer,
      )
          .catchError((error) {
        // Handle initialization error - this is a fallback for direct navigation
        if (mounted) {
          String errorMessage = 'Failed to initialize stream';

          // Check if it's a ban error
          if (error.toString().toLowerCase().contains('banned') ||
              error
                  .toString()
                  .toLowerCase()
                  .contains('you are banned from this stream')) {
            errorMessage = 'You are banned from this stream';
          } else if (error.toString().toLowerCase().contains('forbidden')) {
            errorMessage = 'Access denied to this stream';
          } else {
            errorMessage = 'Failed to join stream: ${error.toString()}';
          }

          _showErrorDialog(errorMessage);
        }
      });
    });
  }

  @override
  void dispose() {
    // Clear callbacks before resetting controller
    controller.onStreamEndedCallback = null;
    controller.onStreamEndedWithReasonCallback = null;

    // Don't dispose the singleton controller, just reset its state
    controller.resetController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent default back behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        await _handleBackPress(context);
      },
      child: CupertinoPageScaffold(
        backgroundColor: Colors.black,
        child: SafeArea(
          child: GestureDetector(
            onTap: () {
              // Hide filter panel when tapping outside
              if (widget.isStreamer &&
                  controller.filterController.isFilterPanelVisible) {
                controller.filterController.hideFilterPanel();
              }
            },
            child: Stack(
              children: [
                // Video view
                _buildVideoView(),

                // Top bar with stream info
                _buildTopBar(),

                // Likes counter for host
                if (widget.isStreamer) _buildLikesCounter(),

                // Join requests notification for host
                if (widget.isStreamer) _buildJoinRequestsNotification(),

                // Chat overlay
                if (_showChat) _buildChatOverlay(),

                // Controls overlay
                if (_showControls) _buildControlsOverlay(),

                // Filter panel (only for streamers)
                if (widget.isStreamer)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: StreamFilterPanel(
                      filterController: controller.filterController,
                    ),
                  ),

                // Like button for viewers (on top of everything)
                if (!widget.isStreamer) _buildLikeButton(),

                // Loading overlay
                ValueListenableBuilder<bool>(
                  valueListenable: controller.isLoading,
                  builder: (context, isLoading, child) {
                    if (isLoading) {
                      return Container(
                        color: Colors.black.withValues(alpha: 0.7),
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CupertinoActivityIndicator(
                                radius: 20,
                                color: Colors.white,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'Connecting...',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoView() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: ValueListenableBuilder<List<int>>(
          valueListenable: controller.remoteUsers,
          builder: (context, remoteUsers, child) {
            if (widget.isStreamer) {
              // Streamer view - show local video
              if (controller.agoraEngine == null) {
                return const Center(
                  child: CupertinoActivityIndicator(
                    radius: 20,
                    color: Colors.white,
                  ),
                );
              }

              return Stack(
                children: [
                  // Local video (streamer's camera) with filters
                  FilteredCameraPreview(
                    filterController: controller.filterController,
                    child: AgoraVideoView(
                      controller: VideoViewController(
                        rtcEngine: controller.agoraEngine!,
                        canvas: const VideoCanvas(uid: 0),
                      ),
                    ),
                  ),
                  // Remote users (if any join as co-hosts)
                  ...remoteUsers.map((uid) => Positioned(
                        top: 100,
                        right: 16,
                        width: 120,
                        height: 160,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: AgoraVideoView(
                            controller: VideoViewController.remote(
                              rtcEngine: controller.agoraEngine!,
                              canvas: VideoCanvas(uid: uid),
                              connection: RtcConnection(
                                channelId: widget.stream.channelName,
                              ),
                            ),
                          ),
                        ),
                      )),
                ],
              );
            } else {
              // Viewer - show streamer's video
              if (controller.agoraEngine == null) {
                return const Center(
                  child: CupertinoActivityIndicator(
                    radius: 20,
                    color: Colors.white,
                  ),
                );
              }

              if (remoteUsers.isNotEmpty) {
                return AgoraVideoView(
                  controller: VideoViewController.remote(
                    rtcEngine: controller.agoraEngine!,
                    canvas: VideoCanvas(uid: remoteUsers.first),
                    connection: RtcConnection(
                      channelId: widget.stream.channelName,
                    ),
                  ),
                );
              } else {
                return ValueListenableBuilder<bool>(
                  valueListenable: controller.streamEnded,
                  builder: (context, streamEnded, child) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            streamEnded
                                ? CupertinoIcons.xmark_circle
                                : CupertinoIcons.video_camera,
                            size: 64,
                            color: streamEnded
                                ? Colors.red.withValues(alpha: 0.7)
                                : Colors.white54,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            streamEnded
                                ? 'Stream has ended'
                                : 'Waiting for stream...',
                            style: TextStyle(
                              color: streamEnded
                                  ? Colors.red.withValues(alpha: 0.7)
                                  : Colors.white54,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              }
            }
          },
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            // Stream info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.stream.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    widget.stream.streamerData.fullName,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Live indicator and viewer count
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: CupertinoColors.systemRed,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    'LIVE',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 8),

            // Viewer count
            ValueListenableBuilder<int>(
              valueListenable: controller.viewerCount,
              builder: (context, count, child) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        CupertinoIcons.eye,
                        size: 12,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$count',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(width: 8),

            // Share button
            GestureDetector(
              onTap: _showShareSheet,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  CupertinoIcons.share,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),

            // Filter button (only for streamers)
            if (widget.isStreamer) ...[
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () => controller.filterController.toggleFilterPanel(),
                child: AnimatedBuilder(
                  animation: controller.filterController,
                  builder: (context, child) {
                    final hasActiveFilter =
                        controller.filterController.hasActiveFilter;
                    return Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: hasActiveFilter
                            ? Colors.purple.withValues(alpha: 0.8)
                            : Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                        border: hasActiveFilter
                            ? Border.all(color: Colors.white, width: 2)
                            : null,
                      ),
                      child: Icon(
                        CupertinoIcons.sparkles,
                        color: Colors.white,
                        size: 18,
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChatOverlay() {
    return Positioned(
      bottom: widget.isStreamer ? 120 : 80,
      left: 16,
      right: 16,
      height: 200,
      child: LiveStreamChat(
        streamId: widget.stream.id,
        isStreamer: widget.isStreamer,
        onToggleChat: () {
          setState(() {
            _showChat = !_showChat;
          });
        },
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: LiveStreamControls(
        isStreamer: widget.isStreamer,
        controller: controller,
        chatController: chatController,
        streamId: widget.stream.id,
        onToggleChat: () {
          setState(() {
            _showChat = !_showChat;
          });
        },
        onEndStream: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showStreamEndedDialog() {
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('Stream Ended'),
          content: const Text(
            'The live stream has ended. You will be redirected back to the streams list.',
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Close stream view
              },
            ),
          ],
        );
      },
    );
  }

  void _handleRemovalFromStream(String? reason, bool isBanned) {
    final action = isBanned ? 'banned from' : 'removed from';
    final message = reason ?? 'You were $action the stream by the host';

    // Show a brief message and automatically redirect
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text(isBanned ? 'Banned from Stream' : 'Removed from Stream'),
          content: Text(message),
          actions: [
            CupertinoDialogAction(
              child: const Text('OK'),
              onPressed: () {
                _navigateToStoriesTab();
              },
            ),
          ],
        );
      },
    );

    // Also automatically redirect after a short delay
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _navigateToStoriesTab();
      }
    });
  }

  void _navigateToStoriesTab() {
    // First close any dialogs
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }

    // Navigate to home and set stories tab
    context.toPageAndRemoveAllWithOutAnimation(const HomeView());

    // Set the tab to stories (index 1) after navigation completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        try {
          final homeController = GetIt.I.get<HomeController>();
          homeController.value.data = 1;
          homeController.update();
        } catch (e) {
          if (kDebugMode) {
            print('Error setting stories tab: $e');
          }
        }
      });
    });
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('Unable to Join Stream'),
          content: Text(message),
          actions: [
            CupertinoDialogAction(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Close stream view
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildLikesCounter() {
    return Positioned(
      top: 100, // Below the top bar
      right: 16,
      child: ValueListenableBuilder<int>(
        valueListenable: controller.likesCount,
        builder: (context, likesCount, child) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  CupertinoIcons.heart_fill,
                  color: CupertinoColors.systemRed,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  '$likesCount',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLikeButton() {
    return Positioned(
      right: 16,
      bottom: 120, // Above the controls
      child: ValueListenableBuilder<bool>(
        valueListenable: controller.isLiked,
        builder: (context, isLiked, child) {
          return ValueListenableBuilder<int>(
            valueListenable: controller.likesCount,
            builder: (context, likesCount, child) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Likes count display
                  if (likesCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$likesCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),

                  if (likesCount > 0) const SizedBox(height: 4),

                  // Like button
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        print('Like button tapped!'); // Debug
                        _handleLikePressed();
                      },
                      borderRadius: BorderRadius.circular(28),
                      child: Container(
                        width: 56,
                        height: 56,
                        decoration: BoxDecoration(
                          color: isLiked
                              ? CupertinoColors.systemRed.withValues(alpha: 0.9)
                              : Colors.black.withValues(alpha: 0.6),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          isLiked
                              ? CupertinoIcons.heart_fill
                              : CupertinoIcons.heart,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  void _handleLikePressed() async {
    print('_handleLikePressed called'); // Debug
    try {
      print('Calling controller.likeStream()'); // Debug
      await controller.likeStream();
      print('Like stream completed successfully'); // Debug
    } catch (e) {
      print('Error in _handleLikePressed: $e'); // Debug
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to like stream: $e'),
            backgroundColor: CupertinoColors.systemRed,
          ),
        );
      }
    }
  }

  Widget _buildJoinRequestsNotification() {
    return Positioned(
      top: 160, // Below the likes counter
      right: 16,
      child: ListenableBuilder(
        listenable: controller,
        builder: (context, child) {
          final joinRequests = controller.joinRequests;
          if (kDebugMode) {
            print(
                "Join requests notification - count: ${joinRequests.length}, requests: $joinRequests");
          }
          if (joinRequests.isEmpty) {
            return const SizedBox.shrink();
          }

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: CupertinoColors.systemBlue.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  CupertinoIcons.person_add,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  '${joinRequests.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () => _showJoinRequestsSheet(),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'View',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showJoinRequestsSheet() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Join Requests'),
        message: const Text('Approve or reject join requests'),
        actions: controller.joinRequests.map((request) {
          if (kDebugMode) {
            print("Processing join request: $request");
          }

          final userName = request['user']?['name'] ??
              request['user']?['username'] ??
              request['userName'] ??
              'Unknown User';
          final requestId =
              request['id'] ?? request['_id'] ?? request['requestId'] ?? '';

          if (kDebugMode) {
            print("Extracted - userName: $userName, requestId: $requestId");
          }

          return CupertinoActionSheetAction(
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: CupertinoColors.systemGrey4,
                  child: Text(
                    userName.isNotEmpty ? userName[0].toUpperCase() : '?',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    userName,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CupertinoButton(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      color: CupertinoColors.systemGreen,
                      borderRadius: BorderRadius.circular(16),
                      child: const Text(
                        'Approve',
                        style: TextStyle(fontSize: 12),
                      ),
                      onPressed: () async {
                        Navigator.of(context).pop();
                        if (requestId.isEmpty) {
                          if (mounted) {
                            VAppAlert.showErrorSnackBar(
                              message: 'Invalid request ID',
                              context: context,
                            );
                          }
                          return;
                        }
                        try {
                          await controller.respondToJoinRequest(
                              requestId, true);
                          if (mounted) {
                            VAppAlert.showErrorSnackBar(
                              message: 'Join request approved',
                              context: context,
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            VAppAlert.showErrorSnackBar(
                              message:
                                  'Failed to approve request: ${e.toString()}',
                              context: context,
                            );
                          }
                        }
                      },
                    ),
                    const SizedBox(width: 8),
                    CupertinoButton(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      color: CupertinoColors.systemRed,
                      borderRadius: BorderRadius.circular(16),
                      child: const Text(
                        'Reject',
                        style: TextStyle(fontSize: 12),
                      ),
                      onPressed: () async {
                        Navigator.of(context).pop();
                        try {
                          await controller.respondToJoinRequest(
                              requestId, false);
                          if (mounted) {
                            VAppAlert.showErrorSnackBar(
                              message: 'Join request rejected',
                              context: context,
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            VAppAlert.showErrorSnackBar(
                              message:
                                  'Failed to reject request: ${e.toString()}',
                              context: context,
                            );
                          }
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            onPressed:
                () {}, // Empty onPressed since we handle it in the buttons
          );
        }).toList(),
        cancelButton: CupertinoActionSheetAction(
          child: const Text('Cancel'),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  void _showShareSheet() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => ShareLiveStreamSheet(
        stream: widget.stream,
        isHost: widget.isStreamer,
      ),
    );
  }

  Future<void> _handleBackPress(BuildContext context) async {
    // If user is a streamer, show confirmation dialog before ending stream
    if (widget.isStreamer) {
      final shouldEndStream = await showCupertinoDialog<bool>(
        context: context,
        builder: (context) => CupertinoAlertDialog(
          title: const Text('End Live Stream'),
          content: const Text(
            'Are you sure you want to end your live stream? This action cannot be undone.',
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              child: const Text('End Stream'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        ),
      );

      if (shouldEndStream == true) {
        try {
          // End the stream
          await controller.endStream();

          // Navigate back to home
          if (mounted) {
            Navigator.of(context).pushAndRemoveUntil(
              CupertinoPageRoute(
                builder: (context) => HomeView(),
              ),
              (route) => false,
            );
          }
        } catch (e) {
          if (mounted) {
            VAppAlert.showErrorSnackBar(
              message: 'Failed to end stream: ${e.toString()}',
              context: context,
            );
          }
        }
      }
    } else {
      // If user is a viewer, just leave the stream
      try {
        await controller.endStream(); // This handles both streamers and viewers

        // Navigate back
        if (mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          VAppAlert.showErrorSnackBar(
            message: 'Failed to leave stream: ${e.toString()}',
            context: context,
          );
          // Still navigate back even if leaving fails
          Navigator.of(context).pop();
        }
      }
    }
  }
}
