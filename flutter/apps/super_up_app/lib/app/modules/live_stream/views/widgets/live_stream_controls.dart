// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import '../../controllers/live_stream_controller.dart';
import '../../controllers/live_stream_chat_controller.dart';
import 'participants_sheet.dart';
import 'live_stream_gift_picker.dart';

class LiveStreamControls extends StatelessWidget {
  final bool isStreamer;
  final LiveStreamController controller;
  final VoidCallback onToggleChat;
  final VoidCallback onEndStream;
  final String streamId;
  final LiveStreamChatController chatController;

  const LiveStreamControls({
    super.key,
    required this.isStreamer,
    required this.controller,
    required this.onToggleChat,
    required this.onEndStream,
    required this.streamId,
    required this.chatController,
  });

  void _showParticipants(BuildContext context) {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (context) => ParticipantsSheet(
        streamId: streamId,
        isStreamer: isStreamer,
        controller: controller,
      ),
    );
  }

  void _showGiftPicker(BuildContext context) {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (context) => LiveStreamGiftPicker(
        chatController: chatController,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Chat toggle button
          _buildControlButton(
            icon: CupertinoIcons.chat_bubble_fill,
            onPressed: onToggleChat,
          ),

          // Participants button (for both streamers and viewers)
          _buildControlButton(
            icon: CupertinoIcons.person_2_fill,
            onPressed: () => _showParticipants(context),
          ),

          if (isStreamer) ...[
            // Mute/Unmute button
            ValueListenableBuilder<bool>(
              valueListenable: controller.isMuted,
              builder: (context, isMuted, child) {
                return _buildControlButton(
                  icon: isMuted
                      ? CupertinoIcons.mic_slash_fill
                      : CupertinoIcons.mic_fill,
                  onPressed: controller.toggleMute,
                  isActive: !isMuted,
                );
              },
            ),

            // Camera toggle button
            ValueListenableBuilder<bool>(
              valueListenable: controller.isCameraOn,
              builder: (context, isCameraOn, child) {
                return _buildControlButton(
                  icon: isCameraOn
                      ? CupertinoIcons.video_camera_solid
                      : CupertinoIcons.video_camera_solid,
                  onPressed: controller.toggleCamera,
                  isActive: isCameraOn,
                );
              },
            ),

            // Switch camera button
            _buildControlButton(
              icon: CupertinoIcons.camera_rotate,
              onPressed: controller.switchCamera,
            ),
          ] else ...[
            // Gift button for viewers
            _buildControlButton(
              icon: CupertinoIcons.gift,
              onPressed: () => _showGiftPicker(context),
              color: Colors.purple,
            ),

            // Speaker toggle for viewers
            ValueListenableBuilder<bool>(
              valueListenable: controller.isSpeakerOn,
              builder: (context, isSpeakerOn, child) {
                return _buildControlButton(
                  icon: isSpeakerOn
                      ? CupertinoIcons.speaker_3_fill
                      : CupertinoIcons.speaker_1_fill,
                  onPressed: controller.toggleSpeaker,
                  isActive: isSpeakerOn,
                );
              },
            ),
          ],

          // End/Leave stream button
          _buildControlButton(
            icon: isStreamer ? CupertinoIcons.stop_fill : CupertinoIcons.xmark,
            onPressed: () => _showEndStreamDialog(context),
            color: CupertinoColors.systemRed,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isActive = true,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: color ??
              (isActive
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.red.withValues(alpha: 0.2)),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: color ?? (isActive ? Colors.white : Colors.red),
          size: 24,
        ),
      ),
    );
  }

  void _showEndStreamDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text(isStreamer ? 'End Live Stream' : 'Leave Stream'),
          content: Text(isStreamer
              ? 'Are you sure you want to end your live stream? This will disconnect all viewers.'
              : 'Are you sure you want to leave this live stream?'),
          actions: [
            CupertinoDialogAction(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              child: Text(isStreamer ? 'End Stream' : 'Leave'),
              onPressed: () {
                Navigator.of(context).pop();
                _handleEndStream(context);
              },
            ),
          ],
        );
      },
    );
  }

  void _handleEndStream(BuildContext context) async {
    try {
      await controller.endStream();
      onEndStream();
    } catch (e) {
      VAppAlert.showErrorSnackBar(
        message: isStreamer ? 'Failed to end stream' : 'Failed to leave stream',
        context: context,
      );
    }
  }
}
